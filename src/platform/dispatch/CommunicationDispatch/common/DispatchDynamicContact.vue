<script lang="ts" setup>
  import { RecycleScroller } from 'vue-virtual-scroller'
  import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
  import type { DynamicContact } from '@/utils/callContact'
  import type { DispatchContactCardEmit } from './DispatchContactCard.vue'
  import { calcScaleSize } from '@/utils/setRem'
  import { computed, ref, useTemplateRef, watch } from 'vue'
  import { useResizeObserver } from '@vueuse/core'
  import { useI18n } from 'vue-i18n'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  import bfDialog from '@/components/bfDialog/main'
  import validateRules from '@/utils/validateRules'
  import { v1 as uuid } from 'uuid'
  import {
    DbOrgIsVirtual,
    MemberType,
    DynamicGroupState,
    addDynamicGroup,
    checkPcDeviceJoinDynamicGroup,
    checkPcDeviceExitDynamicGroup,
    removeDynamicGroup,
    modifyDynamicGroupMember,
  } from '@/utils/dynamicGroup/api'
  import BfCheckbox from '@/components/bfCheckbox/main'
  import BfInput from '@/components/bfInput/main'
  import BfRadio from '@/components/bfRadio/main'
  import BfButton from '@/components/bfButton/main'
  import DialogTableTree from '@/components/common/dialogTableTree.vue'
  import { TreeNodeType } from '@/components/common/tableTree/types'
  import bfutil from '@/utils/bfutil'
  import bfNotify from '@/utils/notify'
  import { speakInfo } from '@/utils/speak'
  import eventBus from '@/utils/eventBus'
  import { insertDbGroupCallContact2db, deleteDbGroupCallContactByOrgId2db } from '@/utils/callContact'
  import { useCommonContact } from '@/utils/callContact/commonContact'
  import openDialog from '@/utils/dialog'
  import { setSpeakTarget, globalVoipServerManager, speakState, speakFast } from '@/utils/speak'

  const dynamicType = {
    100: 'tempGroup',
    101: 'taskGroup',
    0: 'tempGroup',
    1: 'taskGroup',
  }

  const { t } = useI18n()
  const { commonContacts } = useCommonContact()
  const dynamicGroupList = computed<Array<DynamicContact>>(() => {
    const allDynamicData = bfutil.objToArray(bfglob.gorgData.getDynamicGroup())
    return allDynamicData.map(item => {
      const parentOrg = bfglob.gorgData.getDataMaybeNoPerm(item.parentOrgId)
      return {
        rid: item.rid,
        type: dynamicType[item.orgIsVirtual],
        targetRid: item.rid,
        dmrIDHex: item.dmrId,
        name: item.orgShortName,
        parentOrg: parentOrg?.orgShortName ?? item.parentOrgId,
        orgIsVirtual: item.orgIsVirtual,
        dynamicGroupState: item.dynamicGroupState,
      }
    })
  })
  const itemSize = ref(calcScaleSize(132))
  const itemSecondarySize = ref(calcScaleSize(228))
  const dynamicVisible = ref(false)
  const showDeleteAlert = ref(false)
  const isForceDelete = ref(false)
  const isConfirmDelete = ref(false)
  const isEditMode = ref(false)

  const dialogTableTreeRef = useTemplateRef('dialogTableTreeRef')
  // 动态组表单相关
  // const dynamicGroupFormRef = useTemplateRef('dynamicGroupForm')
  const isFastDynamicGroup = ref(false)

  const dynamicGroupData = ref({
    rid: uuid(),
    parentOrgId: bfglob?.userInfo?.orgId || '',
    orgShortName: '',
    dmrId: '',
    orgSortValue: 100,
    note: '',
    // 动态组类型 0:临时组->100 1：任务组->101
    orgIsVirtual: DbOrgIsVirtual.TempGroup,
  })

  const DynamicGroupMemberLimit = {
    100: 16,
    101: 128,
    0: 16,
    1: 128,
  }

  // 动态组成员数据 - 参考dynamic-group-tree.vue的select函数实现
  const devices = ref(new Map())
  const groups = ref(new Map())

  // 计算属性 - 选中的成员总数
  const selectLength = computed(() => devices.value.size + groups.value.size)

  const treeCheckedDmrIds = computed(() => {
    const deviceDmrIds = Array.from(devices.value.values()).map(item => item.deviceDmrid)
    const groupDmrIds = Array.from(groups.value.values()).map(item => item.groupDmrid)
    return [...deviceDmrIds, ...groupDmrIds]
  })

  const emit = defineEmits<DispatchContactCardEmit>()

  const scrollerRef = useTemplateRef('scroller')

  // 参考dynamic-group-tree.vue的setGroupDetail和deleteGroupDetail方法
  const setGroupDetail = (key, data, isGroup = false) => {
    if (isGroup) {
      groups.value.set(key, data)
    } else {
      devices.value.set(key, data)
    }
  }

  const deleteGroupDetail = (key, isGroup = false) => {
    if (isGroup) {
      groups.value.delete(key)
    } else {
      devices.value.delete(key)
    }
  }

  const getDynamicIsExpired = (item: DynamicContact) => {
    return item.orgIsVirtual === DbOrgIsVirtual.TempGroup && item.dynamicGroupState === DynamicGroupState.Expired
  }

  // const dynamicGroupStatus = computed(() => {
  //   // 1: 正常 10: 失效/删除中
  //   return {
  //     [DynamicGroupState.Normal]: t('dynamicGroup.normal'),
  //     [DynamicGroupState.Expired]: t('dynamicGroup.expired'),
  //   }
  // })

  watch(
    () => dynamicGroupList.value,
    () => {
      scrollerRef.value?.updateVisibleItems(true)
    },
    { deep: true }
  )

  useResizeObserver(document.documentElement, () => {
    itemSize.value = calcScaleSize(132)
    itemSecondarySize.value = calcScaleSize(228)
  })

  const openNewDynamicGroupDialog = () => {
    // 重置表单数据
    dynamicGroupData.value = {
      rid: '',
      parentOrgId: bfglob?.userInfo?.orgId || '',
      orgShortName: '',
      dmrId: '',
      orgSortValue: 100,
      note: '',
      orgIsVirtual: DbOrgIsVirtual.TempGroup,
    }

    // 清空成员数据
    devices.value.clear()
    groups.value.clear()

    isFastDynamicGroup.value = false
    isEditMode.value = false
    dynamicVisible.value = true
  }

  // 复选框变化处理 - 参考dynamic-group-tree.vue的select函数实现
  const onCheckboxChange = (row, checked) => {
    // checked 代表勾选或者取消勾选前的状态
    // 选中
    if (!checked) {
      // 如果超出选择上限，则禁止选择
      if (selectLength.value >= maxSelectSize(dynamicGroupData.value.orgIsVirtual)) {
        dialogTableTreeRef.value.tableTreeRef.setCheckboxRowByRid(row.rid, false)
        return
      }

      let target
      let parentKey = 'orgId'
      const isOrg = row.nodeType === TreeNodeType.Org

      if (isOrg) {
        target = bfglob.gorgData.getDataMaybeNoPerm(row.rid)
        // 单位的权限是独立的，组成员的上级设置为自己
        parentKey = 'rid'
      } else {
        target = bfglob.gdevices.getDataMaybeNoPerm(row.rid)
      }

      // 无法查找目标节点源数据
      if (!target) {
        return
      }

      const groupDetail = {
        memberOrgId: target[parentKey],
        dynamicGroupType: dynamicGroupData.value.orgIsVirtual,
      }

      Object.assign(
        groupDetail,
        isOrg
          ? {
              isDeviceGroup: 2,
              groupRid: target.rid,
              groupDmrid: target.dmrId,
            }
          : {
              isDeviceGroup: 1,
              deviceRid: target.rid,
              deviceDmrid: target.dmrId,
            }
      )

      setGroupDetail(row.rid, groupDetail, isOrg)
    } else {
      // 取消勾选
      const isOrg = row.nodeType === TreeNodeType.Org
      deleteGroupDetail(row.rid, isOrg)
    }
  }

  const disbandGroup = (dynamicContact: DynamicContact) => {
    // 通过bfglob.gorgData.get获取完整的动态组数据
    const row = bfglob.gorgData.get(dynamicContact.rid)
    if (!row) {
      bfNotify.messageBox(t('msgbox.delError'), 'error')
      return
    }

    // 将row数据赋值给dynamicGroupData，用于弹窗显示
    dynamicGroupData.value = row

    // 设置删除相关状态
    isForceDelete.value = row.dynamicGroupState === DynamicGroupState.Expired
    isConfirmDelete.value = false

    // 显示删除确认弹窗
    showDeleteAlert.value = true
  }

  const executeDisbandGroup = async () => {
    try {
      const row = dynamicGroupData.value
      const options = {
        beforeSend: rpcCmd => {
          // @ts-expect-error - rpcCmd type is not properly defined in the API
          if (row.dynamicGroupState === DynamicGroupState.Expired || isForceDelete.value) {
            rpcCmd.opt = 'force'
          }
        },
      }
      const rpcCmd = await removeDynamicGroup(row, options)
      bfglob.console.log('[deleteDynamicGroup] res:', rpcCmd)

      if (rpcCmd.resInfo === '+OK') {
        bfNotify.messageBox(t('msgbox.sendSuccess'), 'success')
        // 由服务器通知删除
        // 更新删除中的动态组状态
        // msgData.dynamicGroupState = 10
        // bfglob.emit('update_global_dynamic_group', msgData)
      } else {
        bfNotify.messageBox(t('msgbox.delError'), 'error')
      }
    } catch (err) {
      bfglob.console.warn('[deleteDynamicGroup] err:', err)
      bfNotify.messageBox(t('msgbox.delError'), 'error')
    }
  }

  const removeOrAddFrequently = () => {
    console.log('removeOrAddFrequently')
  }

  const editDynamicGroup = (dynamicContact: DynamicContact) => {
    const data = bfglob.gorgData.get(dynamicContact.rid)
    dynamicGroupData.value = data
    isEditMode.value = true

    // 加载当前动态组的成员数据
    const dynamicDetail = bfglob.gdynamicGroupDetail.getDataByGroupRid(dynamicContact.rid)
    if (dynamicDetail && dynamicDetail.length > 0) {
      // 清空当前选择
      devices.value.clear()
      groups.value.clear()

      // 加载现有成员
      dynamicDetail.forEach(detail => {
        if (detail.isDeviceGroup === MemberType.Device) {
          const device = bfglob.gdevices.get(detail.deviceRid)
          if (device) {
            setGroupDetail(
              detail.rid,
              {
                deviceRid: detail.deviceRid,
                deviceDmrid: device.dmrId,
              },
              false
            )
          }
        } else if (detail.isDeviceGroup === MemberType.Group) {
          const group = bfglob.gorgData.get(detail.groupRid)
          if (group) {
            setGroupDetail(
              detail.rid,
              {
                groupRid: detail.groupRid,
                groupDmrid: group.dmrId,
              },
              true
            )
          }
        }
      })
    }

    // 显示编辑弹窗
    dynamicVisible.value = true
  }

  // 计算属性 - 参考dynamic-group-tree.vue的$getSelectTarget方法
  const memberNodes = computed(() => {
    const devicesArray = Array.from(devices.value.values())
    const groupsArray = Array.from(groups.value.values())
    return generateMemberInfo(groupsArray, devicesArray)
  })

  const generateMemberInfo = (groups, devices) => {
    const list = []
    for (let i = 0; i < devices.length; i++) {
      const device = bfglob.gdevices.get(devices[i].deviceRid)
      list.push({
        name: device.selfId,
        dmrId: device.dmrId,
        dmrIdLabel: `${device.dmrId} / ${Number(`0x${device.dmrId}`) & 0x7fffffff}`,
        rid: device.rid,
        detailRid: device.rid,
        isOrg: false,
      })
    }
    for (let i = 0; i < groups.length; i++) {
      const group = bfglob.gorgData.get(groups[i].groupRid)
      list.push({
        name: group.orgShortName,
        dmrId: group.dmrId,
        dmrIdLabel: `${group.dmrId} / ${Number(`0x${group.dmrId}`) & 0x7fffffff}`,
        rid: group.rid,
        detailRid: group.rid,
        isOrg: true,
      })
    }
    return list
  }

  const rules = computed(() => {
    const orgShortNameRule = []
    if (!isFastDynamicGroup.value) {
      orgShortNameRule.unshift(validateRules.required())
    }

    return {
      orgShortName: orgShortNameRule,
    }
  })

  const orgIsVirtualChange = (val: DbOrgIsVirtual) => {
    if (val === DbOrgIsVirtual.TaskGroup) {
      isFastDynamicGroup.value = false
    }
    dynamicGroupData.value.orgIsVirtual = val
  }

  const getMemberOrgShortName = detail => {
    // 组成员，memberOrgId===rid，需要先查找到自己的数据，再读取上级名称
    let memberOrgId = detail.memberOrgId
    if (detail.isDeviceGroup === MemberType.Group) {
      memberOrgId = bfglob.gdynamicGroupDetail.getDetailSource(detail.rid)?.parentOrgId ?? ''
    }
    return bfglob.gorgData.getDataMaybeNoPerm(memberOrgId)?.orgShortName ?? '-'
  }

  const getMemberOrgName = member => {
    const detail = bfglob.gdynamicGroupDetail.get(member.detailRid)
    if (!detail) {
      if (!member.isOrg) {
        const dev = bfglob.gdevices.get(member.rid)
        if (!dev) return '-'
        return bfglob.gorgData.getShortName(dev.orgId) ?? '-'
      }
      const group = bfglob.gorgData.get(member.rid)
      if (!group) return '-'
      return bfglob.gorgData.get(group.parentOrgId)?.orgShortName ?? '-'
    }
    return getMemberOrgShortName(detail)
  }

  const clickMemberInfo = () => {}

  const removeMember = member => {
    deleteGroupDetail(member.rid, member.isOrg)
  }

  const maxSelectSize = type => {
    return DynamicGroupMemberLimit[type] || DynamicGroupMemberLimit[1]
  }

  const call = (targetDmrId, item: DynamicContact, callback) => {
    // 如果正在呼叫，则直接返回
    if (speakState.current === 1) {
      callback(false)
      return
    }

    // 直接使用 speak.ts 的 speakFast 函数，不再打开 bfSpeaking.vue 对话框
    setSpeakTarget(item.rid)
    speakFast(targetDmrId)
      .then(() => {
        // 只有在通话真正连通后才执行 callback(true)
        callback(true)
      })
      .catch(() => {
        callback(false)
      })
  }

  const hangup = (targetDmrId, item, callback) => {
    if (speakState.current === 1) {
      globalVoipServerManager.sl_i_speak_end()
    }
    callback(true)
  }

  // 发送文本短信函数
  const sendTextMessage = async (targetDmrId: string, item: DynamicContact) => {
    // 打开快速发送命令对话框，指定为文本短信类型
    import('@/platform/dispatch/gisApplication/quickSendCmd.vue').then(DeviceControlComponent => {
      openDialog(DeviceControlComponent.default, { rid: item.rid, cmdType: 'cb31' })
    })
  }

  // 创建快捷临时组成功事件,当前组件订阅使用
  const addFastTempGroupSuccess = 'add-fast-temp-group-success'
  const mergeGroupDetail = (dataList, dynamicGroup) => {
    const dynamicGroupType = dynamicGroup.orgIsVirtual === 100 ? 0 : 1
    return (dataList || []).map(data => {
      return Object.assign(data, {
        rid: data.rid || uuid(),
        orgId: dynamicGroup.rid,
        dynamicGroupType,
      })
    })
  }

  const add = async data => {
    if (selectLength.value <= 0) {
      bfNotify.message(t('dynamicGroup.noGroupMembers'), 'error')
      return
    }

    // 检测组成员是否超出限制
    if (selectLength.value > maxSelectSize(data.orgIsVirtual)) {
      bfNotify.messageBox(t('dynamicGroup.groupMembersLimit'), 'error')
      return
    }

    const rid = uuid()
    const orgData = {
      ...data,
      orgIsVirtual: isFastDynamicGroup.value ? DbOrgIsVirtual.FastTempGroup : data.orgIsVirtual,
      rid,
      orgSortValue: 100,
      dmrId: '',

      // 补齐db_org表字段
      orgSelfId: rid,
      orgFullName: data.orgShortName,
      orgImg: '11111111-1111-1111-1111-111111111111',
      setting: '{}',
    }
    const modifyDeviceList = {
      dynamicGroup: orgData,
      userPriority: speakInfo.priority ?? 2,
      devices: mergeGroupDetail(Array.from(devices.value.values()), orgData),
      groups: mergeGroupDetail(Array.from(groups.value.values()), orgData),
    }

    const resRpcCmd = await addDynamicGroup(modifyDeviceList)
    bfglob.console.log('[addDynamicGroup] res:', resRpcCmd)

    if (resRpcCmd.resInfo === '+OK') {
      bfNotify.messageBox(t('msgbox.addSuccess'), 'success')
      // 添加动态组成功，返回动态组的dmrId
      Object.assign(orgData, resRpcCmd.body.dynamicGroup)
      // 如果是快捷临时组，发布对应的事件
      if (isFastDynamicGroup.value) {
        eventBus.emit(addFastTempGroupSuccess, orgData.dmrId)
        isFastDynamicGroup.value = false
      }

      // DefaultData.orgIsVirtual = DbOrgIsVirtual.FastTempGroup === orgData.orgIsVirtual ? DbOrgIsVirtual.TempGroup : DbOrgIsVirtual.TaskGroup
      dynamicGroupData.value.orgShortName = bfutil.customNumberIncrement(orgData.orgShortName, 1)

      // 每个组或终端只能加入一个动态组，所以需要清楚当前的选中节点
      // this.$refs.dynamicGroupTreeRef?.$selectAll(false)
      bfglob.emit('add_global_dynamic_group', orgData)
      bfglob.emit('dynamic-group-subscribe-server-command', orgData)

      // 发布表格数据变更事件
      // bfprocess.publishTableEvent(this.dataTable.name, 'add', orgData)

      // 处理动态组下成员
      const resDevices = resRpcCmd.body.devices
      const resGroups = resRpcCmd.body.groups
      bfglob.emit('add_global_dynamic_group_detail', [...resDevices, ...resGroups])
      resDevices.forEach(v => {
        checkPcDeviceJoinDynamicGroup(v).catch()
        checkPcDeviceExitDynamicGroup(v).catch()
      })

      // 添加查询日志
      const content = t('dialog.add') + orgData.orgShortName + t('dynamicGroup.title')
      bfglob.emit('addnote', content)

      // // 访问用户是否跳转联网通话
      // this.$nextTick(() => {
      //   this.gotoSpeakerPage(orgData)
      // })

      return orgData
    } else {
      if (commonOperationErr(resRpcCmd)) {
        return
      }
      bfNotify.messageBox(t('msgbox.addError'), 'error')
    }
  }

  const confirmAddDynamicGroup = () => {
    if (isEditMode.value) {
      // 编辑模式 - 调用更新函数
      updateDynamicGroup(dynamicGroupData.value)
        .then(() => {
          devices.value.clear()
          groups.value.clear()
          isEditMode.value = false
          dynamicVisible.value = false
        })
        .catch(err => {
          bfglob.console.error('[updateDynamicGroup] err:', err)
        })
    } else {
      // 新增模式 - 调用添加函数
      add(dynamicGroupData.value)
        .then(() => {
          devices.value.clear()
          groups.value.clear()
          dynamicVisible.value = false
        })
        .catch(err => {
          bfglob.console.error('[addDynamicGroup] err:', err)
        })
    }
  }

  // 更新动态组函数 - 参考DynamicGroup.vue的updateDynamicGroup实现
  const updateDynamicGroup = async data => {
    if (selectLength.value <= 0) {
      bfNotify.message(t('dynamicGroup.noGroupMembers'), 'error')
      return
    }

    const orgData = {
      ...data,
    }

    // 查找出动态组需要添加、删除成员的数据
    const { add, remove } = filterDynamicGroupActionTarget(orgData)

    // 如果没有成员变化，则不需要更新
    if (!(add.devices.length || add.groups.length || remove.devices.length || remove.groups.length)) {
      bfNotify.messageBox(t('dynamicGroup.noUpdates'), 'warning')
      return
    }

    bfglob.console.log('updateDynamicGroup targets:', add, remove)

    const resRpcCmd = await modifyDynamicGroupMember(orgData, add, remove).catch(memberOperationErr)
    bfglob.console.log('updateDynamicGroup res:', resRpcCmd)

    if (resRpcCmd.resInfo.includes('Not allow change member in invalid dyGroup')) {
      bfNotify.messageBox(t('dynamicGroup.taskGroupExpiredWithUpdate'), 'warning')
      orgData.dynamicGroupState = DynamicGroupState.Expired
      bfglob.emit('update_global_dynamic_group', orgData)
      return
    }
    if (resRpcCmd.resInfo.includes('Can not modify auto delete tempGroup')) {
      bfNotify.messageBox(t('dynamicGroup.cannotModifyQuickTempGroup'), 'warning')
      return
    }
    if (resRpcCmd.resInfo.includes('Err:no such dynamic group for:')) {
      bfNotify.messageBox(t('dynamicGroup.noSuchDynamicGroup'), 'warning')
      return
    }
    if (resRpcCmd.resInfo !== '+OK') {
      bfNotify.messageBox(resRpcCmd.resInfo, 'warning')
      return
    }

    bfNotify.messageBox(t('msgbox.sendSuccess'), 'success')

    // 处理更新后的成员数据
    const resDevices = resRpcCmd.body.devices || []
    const resGroups = resRpcCmd.body.groups || []
    bfglob.emit('add_global_dynamic_group_detail', [...resDevices, ...resGroups])

    // 添加操作日志
    const content = t('dialog.edit') + orgData.orgShortName + t('dynamicGroup.title')
    bfglob.emit('addnote', content)
  }

  // 成员操作错误处理 - 参考DynamicGroup.vue的memberOperationErr实现
  const memberOperationErr = rpc_cmd_obj => {
    if (rpc_cmd_obj.resInfo.includes('Can not find this device in dynamic group') || rpc_cmd_obj.resInfo.includes('Can not find dynamicGroup')) {
      bfNotify.warningBox(t('dynamicGroup.notFoundMemberInDynamicGroup'))
      return true
    }
    return false
  }

  // 过滤动态组操作目标 - 参考DynamicGroup.vue的filterDynamicGroupActionTarget实现
  const filterDynamicGroupActionTarget = orgData => {
    // 待返回的结果集
    const result = {
      add: {
        groups: [],
        devices: [],
      },
      remove: {
        groups: [],
        devices: [],
      },
    }

    // 获取当前选择的成员
    const currentDevices = Array.from(devices.value.values())
    const currentGroups = Array.from(groups.value.values())

    // 查找动态组下的所有成员数据
    const groupDetails = bfglob.gdynamicGroupDetail.getDataByGroupRid(orgData.rid) || []

    // 处理删除的成员
    groupDetails.forEach(detail => {
      if (detail.isDeviceGroup === MemberType.Device) {
        // 检查设备是否还在当前选择中
        const found = currentDevices.find(item => item.deviceRid === detail.deviceRid)
        if (!found) {
          result.remove.devices.push(detail)
        }
      } else if (detail.isDeviceGroup === MemberType.Group) {
        // 检查组是否还在当前选择中
        const found = currentGroups.find(item => item.groupRid === detail.groupRid)
        if (!found) {
          result.remove.groups.push(detail)
        }
      }
    })

    // 处理新增的成员
    currentDevices.forEach(device => {
      const found = groupDetails.find(detail => detail.isDeviceGroup === MemberType.Device && detail.deviceRid === device.deviceRid)
      if (!found) {
        result.add.devices.push(device)
      }
    })

    currentGroups.forEach(group => {
      const found = groupDetails.find(detail => detail.isDeviceGroup === MemberType.Group && detail.groupRid === group.groupRid)
      if (!found) {
        result.add.groups.push(group)
      }
    })

    return result
  }

  const commonOperationErr = rpc_cmd_obj => {
    if (rpc_cmd_obj.resInfo.includes('db_org_org_self_id_key')) {
      bfNotify.warningBox(t('msgbox.repeatNo'))
      return true
    }
    if (rpc_cmd_obj.resInfo.includes('db_org_dmr_id_key')) {
      // 服务器生成DMRID异常
      bfNotify.warningBox(t('msgbox.repeatDMRID'))
      return true
    }
    if (rpc_cmd_obj.resInfo.includes('db_org_org_short_name_key')) {
      bfNotify.warningBox(t('dynamicGroup.repeatGroupName'))
      return true
    }
    if (rpc_cmd_obj.resInfo.includes('no login info for this session')) {
      bfNotify.warningBox(t('msgbox.loginSessionExpired'))
      return true
    }
    if (rpc_cmd_obj.resInfo.includes('DatabaseGetSystemDbString')) {
      bfNotify.warningBox(t('dynamicGroup.notFoundDbHandler'))
      return true
    }
    if (rpc_cmd_obj.resInfo.includes('can not get new dmrid for group')) {
      bfNotify.warningBox(t('dynamicGroup.noDynamicGroupDmrId'))
      return true
    }
    if (
      rpc_cmd_obj.resInfo.includes('no such dynamic group') ||
      rpc_cmd_obj.resInfo.includes('no such dynamic group manager') ||
      rpc_cmd_obj.resInfo.includes('Can not find this dynamic group in manager')
    ) {
      bfNotify.warningBox(t('dynamicGroup.notFoundDynamicGroup'))
      return true
    }
    if (rpc_cmd_obj.resInfo.includes('Can not find this temp group in manager')) {
      bfNotify.warningBox(t('dynamicGroup.notFoundDynamicGroup'))
      return true
    }

    return false
  }

  const onCloseDeleteAlertDialog = () => {
    eventBus.emit('delete-alert-dialog-result')
  }

  const showIsForceDelete = computed(() => {
    return dynamicGroupData.value?.orgIsVirtual === DbOrgIsVirtual.TaskGroup
  })

  const disableIsForceDelete = computed(() => {
    // @ts-expect-error - dynamicGroupData.value may be null but we check showIsForceDelete first
    return showIsForceDelete.value && dynamicGroupData.value.dynamicGroupState === DynamicGroupState.Expired
  })

  const confirmDeleteDynamicGroup = () => {
    showDeleteAlert.value = false
    isConfirmDelete.value = true
    // 执行删除操作
    executeDisbandGroup()
  }

  const isInCommonContact = (dynamicContact: DynamicContact) => {
    return commonContacts.value.some(contact => contact.targetRid === dynamicContact.rid)
  }

  function handleAddToCommonContact(dynamicContact: DynamicContact) {
    const inCommonContact = isInCommonContact(dynamicContact)
    if (inCommonContact) {
      deleteDbGroupCallContactByOrgId2db(dynamicContact.rid)
        .then(() => {
          bfNotify.messageBox(t('msgbox.delSuccess'), 'success')
        })
        .catch(() => {
          bfNotify.messageBox(t('msgbox.delError'), 'error')
        })
    } else {
      insertDbGroupCallContact2db(dynamicContact.rid, 0)
        .then(() => {
          bfNotify.messageBox(t('msgbox.addSuccess'), 'success')
        })
        .catch(() => {
          bfNotify.messageBox(t('msgbox.addError'), 'error')
        })
    }
  }
</script>

<template>
  <PageHeader :title="t('dispatch.dynamicGroup')">
    <DispatchTitleIcon icon="bfdx-xinzengyangshineibu" @click="openNewDynamicGroupDialog" />
  </PageHeader>
  <RecycleScroller
    ref="scroller"
    class="contact-container"
    :items="dynamicGroupList"
    :item-size="itemSize"
    :grid-items="2"
    :item-secondary-size="itemSecondarySize"
    center
    key-field="dmrIDHex"
  >
    <template #default="{ item, index }">
      <DispatchContactCard
        v-if="item.dmrIDHex"
        :class="{ 'opacity-50': getDynamicIsExpired(item) }"
        :key="index"
        v-bind="item"
        @locate="targetDmrId => emit('locate', targetDmrId)"
        @call="(targetDmrId, callback) => call(targetDmrId, item, callback)"
        @hangup="(targetDmrId, callback) => hangup(targetDmrId, item, callback)"
        @message="targetDmrId => emit('message', targetDmrId)"
        @send-command="targetDmrId => emit('sendCommand', targetDmrId)"
        @send-message="targetDmrId => sendTextMessage(targetDmrId, item)"
      >
        <template #center-title>
          <ellipsis-text :content="item.orgIsVirtual === DbOrgIsVirtual.TaskGroup ? t('dynamicGroup.taskGroup') : t('dynamicGroup.tempGroup')" />
        </template>

        <template #title>
          <div class="flex justify-center items-center gap-2 rotate-180">
            <el-tooltip popper-class="bf-tooltip" :content="t('dialog.edit')" placement="top" effect="dark">
              <span class="leading-[14px] cursor-pointer bf-iconfont bfdx-dongtaizubianji before:text-[14px]" @click="editDynamicGroup(item)"></span>
            </el-tooltip>
            <el-tooltip
              popper-class="bf-tooltip"
              :content="isInCommonContact(item) ? t('dispatch.contactCard.removeFromCommon') : t('dispatch.contactCard.addToCommon')"
              placement="top"
              effect="dark"
            >
              <span class="leading-[14px] cursor-pointer bf-iconfont bfdx-duijiangjiqiehuan before:text-[14px]" @click="handleAddToCommonContact(item)"></span>
            </el-tooltip>
            <el-tooltip popper-class="bf-tooltip" :content="t('dispatch.contactCard.quitOrDisband')" placement="top" effect="dark">
              <span class="leading-[14px] cursor-pointer bf-iconfont bfdx-duijiangjituichu before:text-[14px]" @click="disbandGroup(item)"></span>
            </el-tooltip>
          </div>
        </template>
      </DispatchContactCard>
    </template>
  </RecycleScroller>
  <bf-dialog
    v-model="dynamicVisible"
    :title="t('dynamicGroup.title')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="dynamic-group-dialog"
    width="720px"
    center
  >
    <div class="flex gap-4 h-[480px]">
      <!-- 左侧表单 -->
      <div class="flex flex-col flex-1">
        <el-form
          ref="dynamicGroupForm"
          :model="dynamicGroupData"
          label-position="top"
          :rules="rules"
          :validate-on-rule-change="false"
          class="flex-none !h-auto"
        >
          <el-form-item :label="t('dialog.type') + ':'" prop="orgIsVirtual">
            <div class="flex gap-4">
              <BfRadio v-model="dynamicGroupData.orgIsVirtual" :value="DbOrgIsVirtual.TaskGroup" @change="orgIsVirtualChange">
                {{ t('dynamicGroup.taskGroup') }}
              </BfRadio>
              <BfRadio v-model="dynamicGroupData.orgIsVirtual" :value="DbOrgIsVirtual.TempGroup" @change="orgIsVirtualChange">
                {{ t('dynamicGroup.tempGroup') }}
              </BfRadio>
            </div>
          </el-form-item>
          <el-form-item :label="t('dialog.name') + ':'" prop="orgShortName">
            <BfInput v-model="dynamicGroupData.orgShortName" :maxlength="16" />
          </el-form-item>
          <el-form-item>
            <BfCheckbox v-model="isFastDynamicGroup" :disabled="dynamicGroupData.orgIsVirtual !== DbOrgIsVirtual.TempGroup">
              {{ t('dynamicGroup.tempGroupInvalidAndDelete') }}
            </BfCheckbox>
          </el-form-item>
        </el-form>

        <!-- 成员信息显示 -->
        <div class="flex-1">
          <div class="text-sm font-medium mb-2">{{ t('dynamicGroup.memberDetails') + ':' }}</div>
          <div class="max-h-[220px] overflow-y-auto">
            <div v-if="selectLength === 0" class="text-gray-500 text-center py-4">
              {{ t('dynamicGroup.noGroupMembers') }}
            </div>
            <div v-else class="space-y-2 members">
              <div v-for="member in memberNodes" :key="member.rid" class="member-item" :title="member.dmrIdLabel" @click="clickMemberInfo">
                <span class="member-item-name">
                  {{ getMemberOrgName(member) + ' / ' + member.name }}
                </span>
                <span class="bf-iconfont bfdx-biaogeshanchushannan before:text-[#FDA216]" @click.stop="removeMember(member)"></span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧树形选择 -->
      <div class="w-[360px] h-[480px] pl-4">
        <div class="text-end h-[22px]">{{ selectLength }}/{{ maxSelectSize(dynamicGroupData.orgIsVirtual) }}</div>
        <DialogTableTree class="!h-[458px]" ref="dialogTableTreeRef" :default-check-keys="treeCheckedDmrIds" @checkbox-change="onCheckboxChange" />
      </div>
    </div>

    <template #footer>
      <div class="flex justify-center gap-3">
        <BfButton color-type="info">
          {{ t('dialog.cancel') }}
        </BfButton>
        <BfButton color-type="primary" @click="confirmAddDynamicGroup">
          {{ t('dialog.confirm') }}
        </BfButton>
      </div>
    </template>
  </bf-dialog>

  <!--  删除动态组的消息提示  -->
  <bf-dialog
    v-model="showDeleteAlert"
    :title="$t('dialog.alertTitle')"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="header-border delete-dynamic-group-dialog"
    width="360px"
    center
    @close="onCloseDeleteAlertDialog"
  >
    <div class="flex justify-center items-center gap-2">
      <el-icon class="text-2xl text-yellow-500">
        <Warning />
      </el-icon>
      <span>{{ $t('dynamicGroup.deleteDynamicGroupTips') }}</span>
    </div>
    <div v-if="showIsForceDelete" class="flex justify-center mt-2">
      <bf-checkbox v-model="isForceDelete" :disabled="disableIsForceDelete">
        {{ $t('dynamicGroup.isForceDelete') }}
      </bf-checkbox>
    </div>

    <template #footer>
      <div class="flex justify-center">
        <bf-button color-type="primary" @click="confirmDeleteDynamicGroup">
          {{ $t('dialog.confirm') }}
        </bf-button>
      </div>
    </template>
  </bf-dialog>
</template>

<style lang="scss" scoped>
  .contact-container {
    height: 100%;
    padding: 10px 53px 0;
    border: 1px solid transparent;
    border-image: linear-gradient(to bottom right, rgba(156, 166, 214, 0.88), rgba(122, 136, 203, 0.46)) 30/1px;

    background: linear-gradient(to bottom right, rgba(0, 0, 11, 0.38), rgba(0, 0, 11, 0.26));
    .card-container {
      margin: 10px;
    }
  }

  .dynamic-group-dialog.el-dialog {
    .members {
      padding-left: 16px;
      width: 100%;
      overflow: auto;

      .member-item {
        line-height: 20px;
        flex-basis: 100%;
        display: flex;
        align-items: center;
        cursor: pointer;

        &:hover {
          .member-item-name,
          .member-item-dmrId {
            color: #20a0ff;
          }
        }

        .member-item-name {
          flex: auto;
          margin-right: 10px;
        }

        .remove-member-action {
          margin-left: 6px;
          cursor: pointer;
          color: #f56c6c;
          font-size: 16px;
        }
      }

      &.is-disabled {
        .member-item {
          cursor: default;

          &:hover {
            .member-item-name,
            .member-item-dmrId {
              color: inherit;
            }
          }

          .remove-member-action {
            display: none;
          }
        }
      }
    }
  }
</style>
